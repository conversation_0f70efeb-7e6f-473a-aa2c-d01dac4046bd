# Engineering Tools Suite

A comprehensive suite of engineering tools for document management, manual creation, and workflow automation.

## Overview

The Engineering Tools Suite is a collection of Python applications designed to streamline engineering documentation workflows. It provides tools for creating technical manuals, managing drawings, monitoring folders for new files, and automating document generation processes. The suite is configured to use JSON files for model definitions and configuration, making it portable and easy to maintain.

All applications feature a modern dark mode interface for improved visibility and reduced eye strain during extended use.

## Features

- **Setup Configuration**: Easy configuration of paths and settings
- **Manual Creation**: Generate technical manuals from templates
- **Drawing Management**: Organize and access engineering drawings
- **File Monitoring**: Watch folders for new files and process them automatically
- **Email Notifications**: Send notifications when new files are processed
- **Model Management**: Add and configure engineering models with associated templates and drawings
- **PDF Section Printing**: Automatically print PDF sections with appropriate settings based on page size
- **Motor FLA Calculation**: Calculate total Full Load Amperage for all motors in a project
- **Project Revision Management**: Create project revisions with automatic backup and version incrementing
- **File Monitoring**: Monitor folders for new/modified files with email notifications
- **DXF Email Distribution**: Automated email sending of DXF files via Outlook
- **Job Data Management**: Create and manage JSON files for project tracking
- **Documentation Status Updates**: Update Obsidian vault documentation status
- **Advanced PDF Operations**: Merge, print, and process PDF documents with intelligent section handling
- **Fill Series Processing**: Batch publishing with auto-incrementing serial numbers
- **Central Launcher**: Access all tools from a single, unified interface
- **Dark Mode Interface**: Improved visibility and reduced eye strain

## Requirements

- Python 3.8 or higher
- Windows operating system
- Required Python packages (see `requirements.txt`)
- Microsoft Word (2016 or higher recommended)

### Microsoft Word Requirements

The Engineering Tools Suite relies heavily on Microsoft Word for document generation and manipulation. Word is required for:

- Opening and using document templates (.dotx files)
- Generating technical manuals from templates
- Converting documents to PDF format
- Inserting drawings and other content into documents

Without Microsoft Word installed, the document generation features will not function correctly. The application uses the Word COM interface through the `pywin32` and `python-docx` libraries to automate document creation.

## Quick Start

1. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```

2. Run the setup application to configure the tool:
   ```
   python setup.py
   ```

3. Launch the Engineering Tools Launcher to access all tools:
   ```
   python apps_launcher.py
   ```

   Or launch individual applications directly from the applications or apps folder.

For detailed installation instructions, see [INSTALL.md](INSTALL.md).

## Project Structure

```
Work-scripts/
├── applications/         # Legacy application modules
├── apps/                 # New application modules
├── lib/                  # Core library modules
├── resources/            # Configuration and resource files
│   ├── config/           # Configuration templates
│   ├── Templates/        # Document templates
│   └── drawings/         # Engineering drawings
├── setup.py              # Setup and configuration script
├── add_model.py          # Tool to add new models
├── apps_launcher.py      # Launcher for all tools in the apps directory
├── utils.py              # Utility functions
├── requirements.txt      # Python dependencies
├── README.md             # This file
└── INSTALL.md            # Detailed installation instructions
```

## Configuration

The Engineering Tools Suite uses several configuration files:

- **models.json**: Contains information about engineering models, templates, and drawing paths. This file has replaced the previous Python-based models.py approach.
- **config.json**: Contains general configuration settings like monitoring folders and email addresses

These files are stored in the user data directory after setup. The application first looks for configuration files in the user data directory, then in the resources/config directory, ensuring portability across different systems.

## Usage

### Setup Application

Run `setup.py` to configure the application with your specific paths and settings:

```
python setup.py
```

### Adding Models

Use the `add_model.py` script to add new engineering models to the system:

```
python add_model.py
```

This tool allows you to:
- Add new equipment models to existing categories
- Create new equipment categories
- Configure template and drawing paths for each model
- Set ASME certification flags and controls parent IDs

### Applications

Applications are available in both the `applications` and `apps` folders:

#### Applications Folder (Legacy)
- **config_app.py**: Configure application settings including template and drawing paths
- **create_Manuals.py**: Create technical manuals from templates with project-specific information
- **Publish 3.py**: Publish engineering projects with title block updates and export to PDF/DXF

#### Apps Folder (New)
- **add_model.py**: Add new engineering models to the system with template and drawing configurations
- **config_app.py**: Configure application settings including template and drawing paths
- **copy_merged_files.py**: Search for MERGED folders and copy files to a manual folder
- **create_job_json.py**: Create JSON files with job data for project tracking and documentation
- **create_manuals.py**: Create technical manuals from templates with project-specific information
- **deep_pdf_merge.py**: Advanced PDF merging capabilities for complex document assembly
- **dxf_emailer.py**: Email DXF files with automated notifications and file management
- **nesting_file_monitor.py**: Monitor folders for new/modified STEP and PDF files and email notifications
- **print_merged_pdf_sections.py**: Print PDF sections with appropriate settings based on page size
- **project_revision_tool.py**: Create project revisions with automatic backup and version incrementing
- **publish_3.py**: Publish engineering projects with title block updates, export to PDF/DXF, and Fill Series batch processing
- **sum_motor_fla.py**: Calculate total FLA for all motors in a project and update attributes
- **update_documentation_status.py**: Update Obsidian vault documentation status for machine manuals

#### Engineering Tools Launcher
The `apps_launcher.py` script provides a central GUI to access all tools in the `apps` directory. Run it with:
```
python apps_launcher.py
```

For detailed documentation on each application, see the [Application Documentation](INSTALL.md#application-documentation) section in INSTALL.md.

## Project Revision Tool

The Project Revision Tool provides an automated way to create project revisions by backing up the current project and creating a new version with an incremented revision number in the filename.

### Features
- Supports both individual files and entire project directories
- Automatically detects existing revision numbers (REV#) in filenames
- Increments revision numbers or adds REV1 for new projects
- Creates timestamped backups in a "revisions" subfolder
- Provides a user-friendly GUI with real-time status updates
- Maintains project structure and file relationships

### Usage

1. Run the application:
   ```
   python apps/project_revision_tool.py
   ```

2. Select either a project file or directory using the appropriate button
3. Review the current revision information displayed
4. Click "Create Revision" to:
   - Move the current project to a timestamped backup in the revisions folder
   - Create a new version with incremented REV# in the filename
   - Update the interface to show the new revision

### Example
- Original: `Project_ABC_REV2.dwg`
- After revision:
  - Backup: `revisions/Project_ABC_REV2_20241220_143022.dwg`
  - New version: `Project_ABC_REV3.dwg`

## PDF Section Printing Application

The PDF Section Printing Application automates the printing of technical documents with varying page sizes and formatting requirements. It detects page types, groups them into sections, and applies appropriate printer settings for each section.

### Features
- Detects page types (Letter, Tabloid, Other) based on dimensions.
- Groups consecutive pages of the same type into sections.
- Extracts sections into temporary files for printing.
- Applies printer settings based on page type.
- Provides a user-friendly GUI for directory selection and printing.

### Usage

1. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```

2. Run the application:
   ```
   python print_merged_pdf_sections.py
   ```

3. Select a directory containing merged PDFs.
4. Click "Print PDF Sections" to start the printing process.

### Testing

Unit tests for the application are located in `test/test_print_merged_pdf_sections.py`. Run the tests using:
```bash
python -m unittest discover -s test
```

## Development

The Engineering Tools Suite is designed to be portable and run on any Windows system after installation. It uses PyInstaller for creating standalone executables.

### Building Executables

To build standalone executables:

```
pyinstaller --onefile --windowed applications/config_app.py
```

For convenience, batch files are provided for compiling each application:

```
compile_publish_3.bat
compile_sum_motor_fla.bat
compile_print_merged_pdf_sections.bat
compile_apps_launcher.bat
```

### Version Control

This project is maintained in a private GitHub repository. All changes should be committed and pushed to the repository to maintain version history and facilitate collaboration.

## License

PROPRIETARY: This software is proprietary and not open-source. For usage and distribution terms, please contact the author.

## Contact

Jonathan Callahan: <EMAIL>

## Tool Categories

### Core Publishing Tools
- **Publish 3**: Complete project publishing with Fill Series batch processing
- **Manual Creator**: Generate technical manuals from templates
- **Project Revision Tool**: Create project revisions with automatic versioning

### File Management Tools
- **Copy Merged Files**: Collect files from MERGED folders
- **DXF Emailer**: Automated DXF file distribution
- **Nesting File Monitor**: Monitor and notify about new/modified files
- **Deep PDF Merge**: Advanced PDF merging capabilities

### Data Management Tools
- **Job Data Creator**: Create JSON files for project tracking
- **Sum Motor FLA**: Calculate total motor amperage
- **Documentation Updater**: Update Obsidian vault status

### Printing and Processing Tools
- **PDF Section Printing**: Intelligent PDF section printing
- **Print Merged PDF Sections**: Advanced PDF printing with size detection

### Configuration Tools
- **Config App**: Configure application settings and paths
- **Add Model**: Add new engineering models to the system

## Documentation

Detailed documentation for individual tools is available in separate README files:

### Core Tools
- [Publish 3 Tool](README_publish_3.md): Documentation for the Publish 3 tool with Fill Series feature
- [Sum Motor FLA Tool](README_sum_motor_fla.md): Documentation for the Sum Motor FLA tool
- [PDF Section Printing](README_PDF_Section_Printing.md): Documentation for the PDF Section Printing tool

### File Management Tools
- [DXF Emailer](README_dxf_emailer.md): Documentation for the DXF email distribution tool
- [Nesting File Monitor](README_nesting_file_monitor.md): Documentation for the file monitoring and notification tool

### Data Management Tools
- [Job Data Creator](README_create_job_json.md): Documentation for the JSON job data creation tool
- [Documentation Updater](README_documentation_updater.md): Documentation for the Obsidian documentation updater

For installation and setup instructions, see [INSTALL.md](INSTALL.md).
