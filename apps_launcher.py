#!/usr/bin/env python3
"""
Engineering Tools Launcher

This application provides a central GUI to access all the engineering tools in both the apps and applications directories.
"""

import os
import sys
import logging
import importlib.util
import subprocess
import traceback
from typing import Dict, List, Callable, Optional, Tuple
import tkinter as tk
from tkinter import messagebox
import customtkinter as ctk

# Add parent directory to path to allow importing from lib and utils
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import utility functions
try:
    from utils import setup_logging, ensure_dir_exists
    from lib.theme_utils import apply_theme
except ImportError:
    try:
        from lib.utils import setup_logging, ensure_dir_exists
        from lib.theme_utils import apply_theme
    except ImportError:
        # Define basic utility functions if import fails
        def ensure_dir_exists(directory):
            """Ensure a directory exists, creating it if necessary"""
            if not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
            return directory

        def setup_logging(log_name="apps_launcher"):
            """Set up basic logging to file and console"""
            log_dir = ensure_dir_exists(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs'))
            log_file = os.path.join(log_dir, f"{log_name}.log")

            logging.basicConfig(
                level=logging.INFO,
                format="%(asctime)s [%(levelname)s] %(message)s",
                handlers=[
                    logging.FileHandler(log_file),
                    logging.StreamHandler(sys.stdout)
                ]
            )
            return log_file

        # Define a basic theme utility if import fails
        def apply_theme(theme_name="red", appearance_mode="dark"):
            ctk.set_appearance_mode(appearance_mode)
            ctk.set_default_color_theme("blue")

class ToolInfo:
    """Class to store information about a tool."""

    def __init__(self, name: str, path: str, description: str = "", category: str = "General", icon_path: Optional[str] = None):
        self.name = name
        self.path = path
        self.description = description
        self.category = category
        self.icon_path = icon_path

class EngineeringToolsLauncher(ctk.CTk):
    """Main application for launching engineering tools."""

    def __init__(self):
        """Initialize the application."""
        super().__init__()

        # Set up the window
        self.title("Engineering Tools Launcher")
        self.geometry("1200x800")
        self.minsize(1000, 700)

        # Apply the red theme
        apply_theme("red", "dark")

        # Initialize variables
        self.tools: Dict[str, ToolInfo] = {}
        self.categories: Dict[str, List[ToolInfo]] = {}

        # Create widgets
        self.create_widgets()

        # Scan for available tools
        self.scan_tools()

        # Log application start
        logging.info("Application started")

    def create_widgets(self):
        """Create the widgets for the application window."""
        # Configure grid layout
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=10)  # Give much more weight to the main frame
        self.grid_rowconfigure(1, weight=1)  # Status bar row

        # Create main frame
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(0, weight=0)  # Title row
        self.main_frame.grid_rowconfigure(1, weight=0)  # Description row
        self.main_frame.grid_rowconfigure(2, weight=10)  # Tabview row - give it much more space

        # Calculate window size based on screen size
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()
        window_width = min(int(screen_width * 0.85), 1600)  # 85% of screen width, max 1600px
        window_height = min(int(screen_height * 0.85), 1000)  # 85% of screen height, max 1000px
        self.geometry(f"{window_width}x{window_height}")

        # Title
        self.title_label = ctk.CTkLabel(
            self.main_frame,
            text="Engineering Tools Launcher",
            font=("Arial", 24, "bold")
        )
        self.title_label.grid(row=0, column=0, pady=(0, 10))

        # Description
        self.desc_label = ctk.CTkLabel(
            self.main_frame,
            text="Select a tool to launch from the list below.",
            font=("Arial", 14)
        )
        self.desc_label.grid(row=1, column=0, pady=(0, 20))

        # Create tabview for tool categories
        self.tabview = ctk.CTkTabview(self.main_frame, segmented_button_fg_color="#C53F3F", segmented_button_selected_color="#A02222", segmented_button_selected_hover_color="#7A1A1A")
        self.tabview.grid(row=2, column=0, padx=10, pady=10, sticky="nsew", rowspan=3)
        # Make tabview taller
        self.tabview.configure(height=window_height - 200)  # Adjust height to fit window

        # Add default tabs
        self.tabview.add("All Tools")
        self.tabview.add("Apps")
        self.tabview.add("Applications")

        # Configure tab content frames
        for tab_name in ["All Tools", "Apps", "Applications"]:
            tab = self.tabview.tab(tab_name)
            tab.grid_columnconfigure(0, weight=1)
            tab.grid_rowconfigure(0, weight=1)

            # Create scrollable frame for tools in this tab
            scrollable_frame = ctk.CTkScrollableFrame(tab)
            scrollable_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")
            # Configure multiple columns for grid layout
            for i in range(4):  # Support up to 4 columns
                scrollable_frame.grid_columnconfigure(i, weight=1)

            # Store reference to the scrollable frame
            setattr(self, f"{tab_name.lower().replace(' ', '_')}_frame", scrollable_frame)

        # Create status bar
        self.status_frame = ctk.CTkFrame(self)
        self.status_frame.grid(row=1, column=0, padx=10, pady=(0, 10), sticky="ew")
        self.status_frame.grid_columnconfigure(0, weight=1)

        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="Ready",
            anchor="w",
            height=30
        )
        self.status_label.grid(row=0, column=0, padx=10, pady=5, sticky="ew")

        # Create refresh button
        self.refresh_btn = ctk.CTkButton(
            self.status_frame,
            text="Refresh Tools",
            command=self.scan_tools,
            width=120,
            height=30,
            corner_radius=8,
            fg_color="#C53F3F",  # Red color
            hover_color="#A02222"  # Darker red
        )
        self.refresh_btn.grid(row=0, column=1, padx=10, pady=5)

    def scan_tools(self):
        """Scan for available tools in both apps and applications directories."""
        # Clear existing tools
        self.tools = {}
        self.categories = {}

        # Clear tool frames
        for frame_name in ["all_tools_frame", "apps_frame", "applications_frame"]:
            frame = getattr(self, frame_name)
            for widget in frame.winfo_children():
                widget.destroy()

        # Scan apps directory
        apps_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "apps")
        apps_tools = self._scan_directory(apps_dir, "Apps")

        # Scan applications directory
        applications_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "applications")
        applications_tools = self._scan_directory(applications_dir, "Applications")

        # Scan Python files in the root directory
        root_dir = os.path.dirname(os.path.abspath(__file__))
        root_tools = []

        # Look for Python files in the root directory
        for file in os.listdir(root_dir):
            if file.endswith('.py') and not file.startswith('__') and file != 'apps_launcher.py':
                file_path = os.path.join(root_dir, file)
                tool_name = self.get_tool_name(file)
                description = self.get_tool_description(file_path)
                tool_info = ToolInfo(tool_name, file_path, description, "Root")
                self.tools[tool_name] = tool_info
                root_tools.append(tool_info)
                if "Root" not in self.categories:
                    self.categories["Root"] = []
                self.categories["Root"].append(tool_info)

        # Add tools to UI
        all_tools = apps_tools + applications_tools + root_tools

        # Sort tools by name
        all_tools.sort(key=lambda x: x.name)

        # Adjust window size based on number of tools if needed
        self._adjust_window_size(len(all_tools))

        # Add to All Tools tab
        self._add_tools_to_frame(all_tools, self.all_tools_frame)

        # Add to Apps tab
        self._add_tools_to_frame(apps_tools, self.apps_frame)

        # Add to Applications tab
        self._add_tools_to_frame(applications_tools, self.applications_frame)

        # Update status
        total_count = len(all_tools)
        self.status_label.configure(
            text=f"Found {total_count} tools ({len(apps_tools)} in apps, {len(applications_tools)} in applications, {len(root_tools)} in root)",
            text_color="#C53F3F"
        )
        logging.info(f"Found {total_count} tools ({len(apps_tools)} in apps, {len(applications_tools)} in applications, {len(root_tools)} in root)")

    def _scan_directory(self, directory: str, category: str) -> List[ToolInfo]:
        """Scan a directory for Python tools and return a list of ToolInfo objects."""
        tools = []

        if not os.path.exists(directory):
            logging.warning(f"Directory not found: {directory}")
            return tools

        # Scan for Python files in the directory
        python_files = [f for f in os.listdir(directory) if f.endswith('.py') and not f.startswith('__')]

        for file in sorted(python_files):
            file_path = os.path.join(directory, file)
            tool_name = self.get_tool_name(file)
            description = self.get_tool_description(file_path)

            tool_info = ToolInfo(tool_name, file_path, description, category)
            self.tools[tool_name] = tool_info
            tools.append(tool_info)

            # Add to categories
            if category not in self.categories:
                self.categories[category] = []
            self.categories[category].append(tool_info)

        return tools

    def _add_tools_to_frame(self, tools: List[ToolInfo], frame: ctk.CTkScrollableFrame):
        """Add tools to a scrollable frame in a grid layout."""
        if not tools:
            # Add a message if no tools found
            no_tools_label = ctk.CTkLabel(
                frame,
                text="No tools found in this category.",
                font=("Arial", 14),
                anchor="center"
            )
            no_tools_label.grid(row=0, column=0, padx=10, pady=20)
            return

        # Configure grid columns for the frame
        # Determine number of columns based on number of tools
        num_tools = len(tools)
        num_columns = min(4, max(2, num_tools // 3 + 1))  # Between 2 and 4 columns

        for i in range(num_columns):
            frame.grid_columnconfigure(i, weight=1)

        # Add tools to the frame in a grid layout
        for i, tool in enumerate(tools):
            row = i // num_columns
            col = i % num_columns

            # Create a frame for this tool
            tool_frame = ctk.CTkFrame(frame)
            tool_frame.grid(row=row, column=col, padx=10, pady=10, sticky="nsew")
            tool_frame.grid_columnconfigure(0, weight=1)

            # Tool name with category badge
            name_frame = ctk.CTkFrame(tool_frame, fg_color="transparent")
            name_frame.grid(row=0, column=0, padx=10, pady=(10, 5), sticky="ew")
            name_frame.grid_columnconfigure(0, weight=1)

            name_label = ctk.CTkLabel(
                name_frame,
                text=tool.name,
                font=("Arial", 16, "bold"),
                anchor="center"
            )
            name_label.grid(row=0, column=0, sticky="ew")

            # Add category badge
            category_colors = {
                "Apps": ("#C53F3F", "#A02222"),  # Red
                "Applications": ("#C53F3F", "#A02222"),  # Red
                "Root": ("#C53F3F", "#A02222")  # Red
            }
            fg_color, hover_color = category_colors.get(tool.category, ("#C53F3F", "#A02222"))  # Default to red

            category_label = ctk.CTkLabel(
                name_frame,
                text=tool.category,
                font=("Arial", 12),
                width=80,
                height=24,
                corner_radius=12,
                fg_color=fg_color,
                text_color="white"
            )
            category_label.grid(row=1, column=0, pady=(0, 5))

            # Description (shortened for grid layout)
            if tool.description:
                # Get first sentence or first 100 characters
                short_desc = tool.description.split(".")[0] + "."
                if len(short_desc) > 100:
                    short_desc = short_desc[:97] + "..."

                desc_label = ctk.CTkLabel(
                    tool_frame,
                    text=short_desc,
                    anchor="center",
                    wraplength=200,
                    justify="center",
                    height=60
                )
                desc_label.grid(row=1, column=0, padx=10, pady=(0, 5), sticky="ew")

            # Add launch button
            launch_btn = ctk.CTkButton(
                tool_frame,
                text="Launch",
                command=lambda t=tool: self.launch_tool(t),
                width=120,
                height=36,
                corner_radius=8,
                fg_color="#C53F3F",  # Red color
                hover_color="#A02222"  # Darker red
            )
            launch_btn.grid(row=2, column=0, padx=10, pady=(5, 10), sticky="ew")

    def get_tool_name(self, filename: str) -> str:
        """Get a user-friendly name for a tool based on its filename."""
        # Special case handling
        special_cases = {
            "Publish 3.py": "Project Publisher",
            "Deep_PDF_Merge.py": "PDF Merger",
            "create_Manuals.py": "Manual Creator",
            "config_app.py": "Configuration Tool",
            "dxf_emailer.py": "DXF Emailer",
            "nesting_file_monitor.py": "Nesting File Monitor",
            "copy_merged_files.py": "MERGED Files Copy Tool",
            "print_merged_pdf_sections.py": "PDF Section Printing",
            "create_job_json.py": "Job Data Creator",
            "sum_motor_fla.py": "Motor FLA Calculator",
            "project_revision_tool.py": "Project Revision Tool"
        }

        if filename in special_cases:
            return special_cases[filename]

        # Default name generation
        name = os.path.splitext(filename)[0].replace('_', ' ')
        return ' '.join(word.capitalize() for word in name.split())

    def get_tool_description(self, file_path: str) -> str:
        """Extract the description from a Python file's docstring."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Look for a docstring
            if '"""' in content:
                docstring = content.split('"""')[1].strip()
                # Return the first paragraph of the docstring
                return docstring.split('\n\n')[0]

            return ""
        except Exception as e:
            logging.error(f"Error reading docstring from {file_path}: {str(e)}")
            return ""

    def _adjust_window_size(self, num_tools: int):
        """Adjust window size based on the number of tools."""
        # Calculate the number of rows needed based on the number of tools and columns
        num_columns = min(4, max(2, num_tools // 3 + 1))  # Between 2 and 4 columns
        num_rows = (num_tools + num_columns - 1) // num_columns  # Ceiling division

        # Get current screen dimensions
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()

        # Calculate window dimensions based on number of tools
        # Each tool card takes approximately 250px width and 200px height
        min_width = min(num_columns * 300 + 100, int(screen_width * 0.9))  # Add padding, max 90% of screen
        min_height = min(num_rows * 220 + 300, int(screen_height * 0.9))  # Add space for header/footer, max 90% of screen

        # Set window size
        window_width = max(min_width, 1000)  # Minimum width of 1000px
        window_height = max(min_height, 700)  # Minimum height of 700px

        # Update window geometry
        self.geometry(f"{window_width}x{window_height}")

        # Update tabview height
        self.tabview.configure(height=window_height - 200)  # Adjust height to fit window

        logging.info(f"Adjusted window size to {window_width}x{window_height} for {num_tools} tools")

    def launch_tool(self, tool: ToolInfo):
        """Launch a tool."""
        try:
            logging.info(f"Launching tool: {tool.name} ({tool.path})")
            self.status_label.configure(text=f"Launching {tool.name}...", text_color="#2AA876")

            # Launch the tool in a separate process
            subprocess.Popen([sys.executable, tool.path], creationflags=subprocess.CREATE_NEW_CONSOLE)

            self.status_label.configure(text=f"Launched {tool.name}", text_color="#2AA876")
        except Exception as e:
            logging.error(f"Error launching {tool.name}: {str(e)}")
            self.status_label.configure(text=f"Error launching {tool.name}: {str(e)}", text_color="#FF0000")
            messagebox.showerror("Launch Error", f"Error launching {tool.name}: {str(e)}")

def main():
    """Main entry point for the application."""
    try:
        # Set up logging
        log_file = setup_logging("apps_launcher")
        logging.info("Starting Engineering Tools Launcher")

        # Create and run the application
        app = EngineeringToolsLauncher()
        app.mainloop()

        logging.info("Application closed")
    except Exception as e:
        logging.error(f"Unhandled exception: {e}")
        logging.debug(traceback.format_exc())
        print(f"An error occurred: {e}")
        print(f"See log file for details: {log_file}")
        sys.exit(1)

if __name__ == "__main__":
    main()
